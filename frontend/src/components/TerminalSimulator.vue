<template>
  <div class="terminal-simulator">
    <div class="terminal-header">
      <h2>Terminal Simulator</h2>
      <div class="terminal-info">
        <span>Serial: {{ selectedTerminal?.serialNumber || 'Nicht ausgewählt' }}</span>
        <span>Status: {{ terminalState }}</span>
      </div>
    </div>

    <!-- Terminal Selection -->
    <div class="terminal-selection">
      <label for="terminal-select">Terminal auswählen:</label>
      <select id="terminal-select" v-model="selectedTerminal" @change="onTerminalChange">
        <option :value="null">-- Terminal auswählen --</option>
        <option v-for="terminal in terminals" :key="terminal.documentId" :value="terminal">
          {{ terminal.serialNumber }} - {{ terminal.terminalName }}
        </option>
      </select>
    </div>

    <!-- Terminal Screen -->
    <div class="terminal-screen" v-if="selectedTerminal">
      <div class="screen-content">
        <h3>{{ currentScreen.title }}</h3>
        <p>{{ currentScreen.message }}</p>
        
        <!-- Screen-specific content -->
        <div v-if="currentScreen.id === 'screen-list-charging-points'" class="charging-points">
          <div v-for="evse in evses" :key="evse.documentId" class="charging-point">
            <button @click="selectChargingPoint(evse)" class="charging-point-btn">
              {{ evse.labelForTerminal || evse.evseId }}
            </button>
          </div>
        </div>

        <div v-if="currentScreen.id === 'screen-reading'" class="card-reading">
          <div class="card-animation">💳</div>
          <p>Karte vorhalten...</p>
          <button @click="simulateCardRead" class="simulate-btn">Karte simulieren</button>
        </div>

        <div v-if="currentScreen.id === 'screen-charging-started'" class="charging-info">
          <p>Ladevorgang läuft...</p>
          <p>{{ chargingInfo.kwh }} kWh</p>
          <p>{{ chargingInfo.duration }}</p>
        </div>
      </div>

      <!-- Terminal Buttons -->
      <div class="terminal-buttons">
        <button v-for="button in currentScreen.buttons" 
                :key="button.id" 
                @click="onButtonClick(button.id)"
                class="terminal-btn">
          {{ button.label }}
        </button>
      </div>
    </div>

    <!-- API Logs -->
    <div class="api-logs">
      <h3>API Kommunikation</h3>
      <div class="log-controls">
        <button @click="clearLogs">Logs löschen</button>
        <label>
          <input type="checkbox" v-model="autoScroll"> Auto-Scroll
        </label>
      </div>
      <div class="logs-container" ref="logsContainer">
        <div v-for="log in apiLogs" :key="log.id" :class="['log-entry', log.type]">
          <span class="timestamp">{{ log.timestamp }}</span>
          <span class="direction">{{ log.direction }}</span>
          <span class="endpoint">{{ log.endpoint }}</span>
          <pre class="payload">{{ JSON.stringify(log.payload, null, 2) }}</pre>
        </div>
      </div>
    </div>

    <!-- Control Panel -->
    <div class="control-panel">
      <h3>Simulator Kontrollen</h3>
      <div class="controls">
        <button @click="sendStateCallback('IDLE')" class="control-btn">IDLE State</button>
        <button @click="sendStateCallback('BUSY')" class="control-btn">BUSY State</button>
        <button @click="sendStateCallback('ERROR')" class="control-btn">ERROR State</button>
        <button @click="simulatePaymentSuccess" class="control-btn">Payment Success</button>
        <button @click="simulatePaymentError" class="control-btn">Payment Error</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick, watch } from 'vue'
import { apiClient } from '~/services/api'

// Types
interface Terminal {
  documentId: string
  serialNumber: string
  terminalName: string
  mandant: any
  evses: any[]
}

interface ScreenData {
  id: string
  title: string
  message: string
  buttons: Array<{ id: string; label: string }>
}

interface ApiLog {
  id: number
  timestamp: string
  direction: 'outgoing' | 'incoming'
  endpoint: string
  payload: any
  type: 'success' | 'error' | 'info'
}

// Reactive data
const terminals = ref<Terminal[]>([])
const selectedTerminal = ref<Terminal | null>(null)
const terminalState = ref('OFFLINE')
const evses = ref([])
const apiLogs = ref<ApiLog[]>([])
const autoScroll = ref(true)
const logsContainer = ref<HTMLElement>()

let logIdCounter = 0

const currentScreen = reactive<ScreenData>({
  id: 'screen-init',
  title: 'Terminal bereit',
  message: 'Wählen Sie eine Option',
  buttons: [
    { id: 'charging-points', label: 'Ladepunkte' },
    { id: 'info', label: 'Info' }
  ]
})

const chargingInfo = reactive({
  kwh: '0.00',
  duration: '00:00:00'
})

// Methods
const loadTerminals = async () => {
  try {
    const response = await apiClient.get('/api/terminals', {
      params: {
        populate: {
          mandant: { fields: ['name'] },
          evses: { fields: ['evseId', 'labelForTerminal'] }
        }
      }
    })
    terminals.value = response.data.data
    addLog('outgoing', 'GET /api/terminals', response.data, 'success')
  } catch (error) {
    console.error('Error loading terminals:', error)
    addLog('outgoing', 'GET /api/terminals', error, 'error')
  }
}

const onTerminalChange = () => {
  if (selectedTerminal.value) {
    evses.value = selectedTerminal.value.evses || []
    terminalState.value = 'ACTIVE'
    showScreen('screen-init')
  }
}

const showScreen = (screenId: string) => {
  const screens = {
    'screen-init': {
      id: 'screen-init',
      title: 'Terminal bereit',
      message: 'Wählen Sie eine Option',
      buttons: [
        { id: 'charging-points', label: 'Ladepunkte' },
        { id: 'info', label: 'Info' }
      ]
    },
    'screen-list-charging-points': {
      id: 'screen-list-charging-points',
      title: 'Ladepunkte auswählen',
      message: 'Wählen Sie einen Ladepunkt',
      buttons: [
        { id: 'back', label: 'Zurück' }
      ]
    },
    'screen-reading': {
      id: 'screen-reading',
      title: 'Karte lesen',
      message: 'Bitte halten Sie Ihre Karte vor das Terminal',
      buttons: [
        { id: 'cancel', label: 'Abbrechen' }
      ]
    },
    'screen-charging-started': {
      id: 'screen-charging-started',
      title: 'Ladevorgang aktiv',
      message: 'Ihr Fahrzeug wird geladen',
      buttons: [
        { id: 'stop', label: 'Stoppen' }
      ]
    }
  }

  Object.assign(currentScreen, screens[screenId] || screens['screen-init'])
}

const onButtonClick = async (buttonId: string) => {
  if (!selectedTerminal.value) return

  const payload = {
    serialNumber: selectedTerminal.value.serialNumber,
    screenId: currentScreen.id,
    response: buttonId,
    timestamp: new Date().toISOString()
  }

  try {
    // Send UI callback
    const response = await apiClient.post('/api/payter/uiCallback', payload)
    addLog('incoming', 'POST /api/payter/uiCallback', payload, 'info')
    addLog('outgoing', 'Response', response.data, 'success')

    // Handle button actions
    switch (buttonId) {
      case 'charging-points':
        showScreen('screen-list-charging-points')
        break
      case 'back':
        showScreen('screen-init')
        break
      case 'cancel':
        showScreen('screen-init')
        break
    }
  } catch (error) {
    console.error('Error sending UI callback:', error)
    addLog('incoming', 'POST /api/payter/uiCallback', error, 'error')
  }
}

const selectChargingPoint = async (evse: any) => {
  showScreen('screen-reading')
  
  // Simulate API call to start card reading
  const payload = {
    evseId: evse.evseId,
    terminalId: selectedTerminal.value?.serialNumber
  }
  
  addLog('outgoing', 'Start Card Reading', payload, 'info')
}

const simulateCardRead = async () => {
  if (!selectedTerminal.value) return

  const cardData = {
    serialNumber: selectedTerminal.value.serialNumber,
    cardId: 'CARD123456789',
    brand: 'VISA',
    maskedPan: '****1234',
    sessionId: `session_${Date.now()}`,
    timestamp: new Date().toISOString()
  }

  try {
    const response = await apiClient.post('/api/payter/cardInfoCallback', cardData)
    addLog('incoming', 'POST /api/payter/cardInfoCallback', cardData, 'info')
    addLog('outgoing', 'Response', response.data, 'success')
    
    // Simulate charging start
    setTimeout(() => {
      showScreen('screen-charging-started')
      startChargingSimulation()
    }, 2000)
  } catch (error) {
    console.error('Error sending card info:', error)
    addLog('incoming', 'POST /api/payter/cardInfoCallback', error, 'error')
  }
}

const sendStateCallback = async (state: string) => {
  if (!selectedTerminal.value) return

  const payload = {
    serialNumber: selectedTerminal.value.serialNumber,
    state: state,
    timestamp: new Date().toISOString()
  }

  try {
    const response = await apiClient.post('/api/payter/stateCallback', payload)
    addLog('incoming', 'POST /api/payter/stateCallback', payload, 'info')
    addLog('outgoing', 'Response', response.data, 'success')
    terminalState.value = state
  } catch (error) {
    console.error('Error sending state callback:', error)
    addLog('incoming', 'POST /api/payter/stateCallback', error, 'error')
  }
}

const simulatePaymentSuccess = async () => {
  if (!selectedTerminal.value) return

  const payload = {
    session: {
      serialNumber: selectedTerminal.value.serialNumber,
      sessionId: `session_${Date.now()}`,
      state: 'AUTHORIZED',
      result: 'APPROVED',
      authorizedAmount: 5000,
      cardId: 'CARD123456789',
      brand: 'VISA',
      authorizationCode: 'AUTH123',
      merchantReference: evses.value[0]?.documentId || null
    },
    timestamp: new Date().toISOString()
  }

  try {
    const response = await apiClient.post('/api/payter/authorizeCallback', payload)
    addLog('incoming', 'POST /api/payter/authorizeCallback', payload, 'info')
    addLog('outgoing', 'Response', response.data, 'success')
  } catch (error) {
    console.error('Error sending authorize callback:', error)
    addLog('incoming', 'POST /api/payter/authorizeCallback', error, 'error')
  }
}

const simulatePaymentError = async () => {
  if (!selectedTerminal.value) return

  const payload = {
    session: {
      serialNumber: selectedTerminal.value.serialNumber,
      sessionId: `session_${Date.now()}`,
      state: 'DECLINED',
      result: 'DECLINED',
      errorCode: 'INSUFFICIENT_FUNDS'
    },
    timestamp: new Date().toISOString()
  }

  try {
    const response = await apiClient.post('/api/payter/authorizeCallback', payload)
    addLog('incoming', 'POST /api/payter/authorizeCallback', payload, 'info')
    addLog('outgoing', 'Response', response.data, 'success')
  } catch (error) {
    console.error('Error sending authorize callback:', error)
    addLog('incoming', 'POST /api/payter/authorizeCallback', error, 'error')
  }
}

const startChargingSimulation = () => {
  let kwh = 0
  let seconds = 0
  
  const interval = setInterval(() => {
    kwh += 0.1
    seconds += 10
    
    chargingInfo.kwh = kwh.toFixed(1)
    chargingInfo.duration = new Date(seconds * 1000).toISOString().substr(11, 8)
  }, 1000)

  // Stop after 30 seconds for demo
  setTimeout(() => {
    clearInterval(interval)
    showScreen('screen-init')
  }, 30000)
}

const addLog = (direction: 'outgoing' | 'incoming', endpoint: string, payload: any, type: 'success' | 'error' | 'info') => {
  apiLogs.value.push({
    id: logIdCounter++,
    timestamp: new Date().toLocaleTimeString(),
    direction,
    endpoint,
    payload,
    type
  })

  if (autoScroll.value) {
    nextTick(() => {
      if (logsContainer.value) {
        logsContainer.value.scrollTop = logsContainer.value.scrollHeight
      }
    })
  }
}

const clearLogs = () => {
  apiLogs.value = []
  logIdCounter = 0
}

// Lifecycle
onMounted(() => {
  loadTerminals()
})
</script>
