import screens from "../../payter/config/payter-screens.json";
import type { Data } from '@strapi/strapi';
import {displayScreen} from "../../payter/controllers/terminal/uiActions";

export default {
    /**
     * Push the terminal screen
     */
    pushQrCode: async (paymentSession: Data.ContentType<'api::payment-session.payment-session'>) => {
        try{

        const chargingStartedScreen = screens.screens.de.find(screen => screen.id === 'screen-charging-started');
        if (chargingStartedScreen) {
            const screenWithSessionId = JSON.parse(JSON.stringify(chargingStartedScreen));

            // Verwende die Payment-Session-ID aus der Payter-Antwort, falls vorhanden
            screenWithSessionId.properties.qr = strapi.config.get('server.frontendURL') + `/${paymentSession.mandant.name}/session/${paymentSession.paymentIntent}`;

            let ctx = {
                state: {
                    terminal: paymentSession.terminal
                }
            }

            const { apiClient, callback } = await strapi.service('api::payter.api-client').getPayterApiClient(ctx);

            const params = new URLSearchParams({ callbackUrl: strapi.config.get('server.frontendURL') + '/api/payter/init' });

            const response = await apiClient.post(`/terminals/${paymentSession.terminal.serialNumber}/ui?${callback.ui}?${params.toString()}`, screenWithSessionId);

            // Warte 10 Sekunden und wechsel dann den Screen auf "screen-init"
            await new Promise(resolve => setTimeout(resolve, 10_000));
            ctx = {
                state: {
                    terminal: paymentSession.terminal
                }
            }
            await displayScreen(ctx, apiClient, callback.ui, 'screen-init');

            console.log('Charging started screen displayed:', response.data);
        }
        } catch (error) {
            console.error('Error pushing terminal screen:', error);
            // Hier kannst du den Fehler behandeln, z.B. eine Fehlermeldung an den Client zurückgeben
        }

        return;
    },

}
